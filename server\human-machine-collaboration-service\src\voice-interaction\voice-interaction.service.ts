import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { VoiceCommand } from '../database/entities/voice-command.entity';
import { SpeechRecognitionService } from './speech-recognition.service';
import { TextToSpeechService } from './text-to-speech.service';
import { VoiceCommandProcessor } from './voice-command-processor.service';

/**
 * 语音交互服务
 * 统一管理语音识别、合成和指令处理
 */
@Injectable()
export class VoiceInteractionService {
  private readonly logger = new Logger(VoiceInteractionService.name);

  constructor(
    @InjectRepository(VoiceCommand)
    private readonly voiceCommandRepository: Repository<VoiceCommand>,
    private readonly speechRecognitionService: SpeechRecognitionService,
    private readonly textToSpeechService: TextToSpeechService,
    private readonly voiceCommandProcessor: VoiceCommandProcessor,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 处理语音输入
   * @param audioData 音频数据
   * @param userId 用户ID
   * @param sessionId 会话ID
   * @returns 处理结果
   */
  async processVoiceInput(
    audioData: Buffer | string,
    userId: string,
    sessionId?: string,
  ): Promise<any> {
    try {
      const startTime = Date.now();

      // 1. 语音识别
      const recognitionResult = await this.speechRecognitionService.recognize(audioData);
      
      if (!recognitionResult.success) {
        return {
          success: false,
          error: '语音识别失败',
          details: recognitionResult.error,
        };
      }

      // 2. 指令处理
      const commandResult = await this.voiceCommandProcessor.processCommand(
        recognitionResult.text,
        userId,
        sessionId,
      );

      // 3. 生成语音响应
      let audioResponse = null;
      if (commandResult.response) {
        audioResponse = await this.textToSpeechService.synthesize(
          commandResult.response,
          {
            language: recognitionResult.language || 'zh-CN',
            voice: this.configService.get('TTS_VOICE', 'zh-CN-XiaoxiaoNeural'),
          },
        );
      }

      const processingTime = Date.now() - startTime;

      // 4. 保存语音指令记录
      const voiceCommand = await this.saveVoiceCommand({
        userId,
        sessionId,
        originalAudio: typeof audioData === 'string' ? audioData : audioData.toString('base64'),
        recognizedText: recognitionResult.text,
        recognitionConfidence: recognitionResult.confidence,
        commandType: commandResult.type,
        extractedCommand: commandResult.command,
        parameters: commandResult.parameters,
        success: commandResult.success,
        response: commandResult.response,
        processingTime,
        language: recognitionResult.language,
        audioMetadata: recognitionResult.metadata,
        errorInfo: commandResult.error,
      });

      return {
        success: true,
        id: voiceCommand.id,
        recognizedText: recognitionResult.text,
        confidence: recognitionResult.confidence,
        command: commandResult.command,
        response: commandResult.response,
        audioResponse: audioResponse?.audioData,
        processingTime,
        suggestions: commandResult.suggestions,
      };

    } catch (error) {
      this.logger.error('处理语音输入失败:', error);
      throw error;
    }
  }

  /**
   * 文本转语音
   * @param text 文本内容
   * @param options 合成选项
   * @returns 音频数据
   */
  async textToSpeech(text: string, options?: any): Promise<any> {
    try {
      return await this.textToSpeechService.synthesize(text, {
        language: options?.language || 'zh-CN',
        voice: options?.voice || this.configService.get('TTS_VOICE'),
        speed: options?.speed || 1.0,
        pitch: options?.pitch || 1.0,
        volume: options?.volume || 1.0,
      });
    } catch (error) {
      this.logger.error('文本转语音失败:', error);
      throw error;
    }
  }

  /**
   * 语音转文本
   * @param audioData 音频数据
   * @param options 识别选项
   * @returns 识别结果
   */
  async speechToText(audioData: Buffer | string, options?: any): Promise<any> {
    try {
      return await this.speechRecognitionService.recognize(audioData, {
        language: options?.language || 'zh-CN',
        enablePunctuation: options?.enablePunctuation !== false,
        enableWordTimeOffsets: options?.enableWordTimeOffsets || false,
      });
    } catch (error) {
      this.logger.error('语音转文本失败:', error);
      throw error;
    }
  }

  /**
   * 获取支持的语言列表
   */
  async getSupportedLanguages(): Promise<any> {
    try {
      const speechLanguages = await this.speechRecognitionService.getSupportedLanguages();
      const ttsLanguages = await this.textToSpeechService.getSupportedLanguages();

      return {
        speechRecognition: speechLanguages,
        textToSpeech: ttsLanguages,
        common: speechLanguages.filter(lang => 
          ttsLanguages.some(ttsLang => ttsLang.code === lang.code)
        ),
      };
    } catch (error) {
      this.logger.error('获取支持语言失败:', error);
      throw error;
    }
  }

  /**
   * 获取语音指令历史
   * @param userId 用户ID
   * @param limit 限制数量
   * @returns 指令历史
   */
  async getVoiceCommandHistory(userId: string, limit: number = 20): Promise<VoiceCommand[]> {
    try {
      return await this.voiceCommandRepository.find({
        where: { userId },
        order: { createdAt: 'DESC' },
        take: limit,
      });
    } catch (error) {
      this.logger.error('获取语音指令历史失败:', error);
      throw error;
    }
  }

  /**
   * 获取语音交互统计
   * @param userId 用户ID（可选）
   * @returns 统计信息
   */
  async getVoiceInteractionStats(userId?: string): Promise<any> {
    try {
      const queryBuilder = this.voiceCommandRepository.createQueryBuilder('vc');

      if (userId) {
        queryBuilder.where('vc.userId = :userId', { userId });
      }

      const totalCommands = await queryBuilder.getCount();
      
      const successRate = await queryBuilder
        .select('AVG(CASE WHEN vc.success = 1 THEN 1.0 ELSE 0.0 END)', 'successRate')
        .getRawOne();

      const avgConfidence = await queryBuilder
        .select('AVG(vc.recognitionConfidence)', 'avgConfidence')
        .getRawOne();

      const avgProcessingTime = await queryBuilder
        .select('AVG(vc.processingTime)', 'avgProcessingTime')
        .getRawOne();

      const commandTypes = await queryBuilder
        .select('vc.commandType', 'type')
        .addSelect('COUNT(*)', 'count')
        .groupBy('vc.commandType')
        .getRawMany();

      return {
        totalCommands,
        successRate: parseFloat(successRate?.successRate || '0'),
        averageConfidence: parseFloat(avgConfidence?.avgConfidence || '0'),
        averageProcessingTime: parseFloat(avgProcessingTime?.avgProcessingTime || '0'),
        commandTypeDistribution: commandTypes,
      };

    } catch (error) {
      this.logger.error('获取语音交互统计失败:', error);
      throw error;
    }
  }

  /**
   * 配置语音参数
   * @param userId 用户ID
   * @param settings 语音设置
   */
  async configureVoiceSettings(userId: string, settings: any): Promise<void> {
    try {
      // 这里可以保存用户的语音偏好设置
      // 例如：语音、语速、音调等
      this.logger.log(`用户 ${userId} 配置语音设置:`, settings);
      
      // 实际实现中可以保存到数据库或缓存
    } catch (error) {
      this.logger.error('配置语音设置失败:', error);
      throw error;
    }
  }

  /**
   * 保存语音指令记录
   */
  private async saveVoiceCommand(data: any): Promise<VoiceCommand> {
    try {
      const voiceCommand = this.voiceCommandRepository.create(data);
      return await this.voiceCommandRepository.save(voiceCommand);
    } catch (error) {
      this.logger.error('保存语音指令记录失败:', error);
      throw error;
    }
  }
}
