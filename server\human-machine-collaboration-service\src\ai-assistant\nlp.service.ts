import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as natural from 'natural';

/**
 * 自然语言处理服务
 * 提供文本分析、意图识别、实体提取等功能
 */
@Injectable()
export class NLPService {
  private readonly logger = new Logger(NLPService.name);
  private tokenizer: any;
  private stemmer: any;
  private classifier: any;

  constructor(private readonly configService: ConfigService) {
    this.initializeNLP();
  }

  /**
   * 初始化NLP组件
   */
  private initializeNLP(): void {
    try {
      // 初始化分词器
      this.tokenizer = new natural.WordTokenizer();
      
      // 初始化词干提取器
      this.stemmer = natural.PorterStemmer;
      
      // 初始化分类器
      this.classifier = new natural.LogisticRegressionClassifier();
      
      // 训练意图分类器
      this.trainIntentClassifier();
      
      this.logger.log('NLP组件初始化完成');
    } catch (error) {
      this.logger.error('初始化NLP组件失败:', error);
    }
  }

  /**
   * 训练意图分类器
   */
  private trainIntentClassifier(): void {
    // 维护相关训练数据
    this.classifier.addDocument('如何维护设备', 'maintenance_question');
    this.classifier.addDocument('设备保养步骤', 'maintenance_question');
    this.classifier.addDocument('维护手册在哪里', 'maintenance_question');
    this.classifier.addDocument('定期检查项目', 'maintenance_question');
    
    // 故障排除相关训练数据
    this.classifier.addDocument('设备不工作', 'troubleshooting_request');
    this.classifier.addDocument('出现错误代码', 'troubleshooting_request');
    this.classifier.addDocument('机器故障', 'troubleshooting_request');
    this.classifier.addDocument('设备异常', 'troubleshooting_request');
    
    // 培训相关训练数据
    this.classifier.addDocument('如何学习操作', 'training_inquiry');
    this.classifier.addDocument('培训课程', 'training_inquiry');
    this.classifier.addDocument('操作指导', 'training_inquiry');
    this.classifier.addDocument('技能培训', 'training_inquiry');
    
    // 一般支持相关训练数据
    this.classifier.addDocument('需要帮助', 'general_support');
    this.classifier.addDocument('使用说明', 'general_support');
    this.classifier.addDocument('操作指南', 'general_support');
    this.classifier.addDocument('功能介绍', 'general_support');
    
    // 训练分类器
    this.classifier.train();
  }

  /**
   * 分析消息
   * @param message 用户消息
   * @returns 分析结果
   */
  async analyzeMessage(message: string): Promise<any> {
    try {
      // 文本预处理
      const cleanedMessage = this.preprocessText(message);
      
      // 分词
      const tokens = this.tokenizer.tokenize(cleanedMessage);
      
      // 词干提取
      const stems = tokens.map(token => this.stemmer.stem(token));
      
      // 情感分析
      const sentiment = this.analyzeSentiment(message);
      
      // 关键词提取
      const keywords = this.extractKeywords(tokens);
      
      return {
        originalMessage: message,
        cleanedMessage,
        tokens,
        stems,
        sentiment,
        keywords,
        language: this.detectLanguage(message),
      };
      
    } catch (error) {
      this.logger.error('分析消息失败:', error);
      throw error;
    }
  }

  /**
   * 提取意图
   * @param message 用户消息
   * @returns 意图信息
   */
  async extractIntent(message: string): Promise<any> {
    try {
      const cleanedMessage = this.preprocessText(message);
      const classifications = this.classifier.getClassifications(cleanedMessage);
      
      // 获取最高置信度的意图
      const topIntent = classifications[0];
      
      return {
        name: topIntent.label,
        confidence: topIntent.value,
        alternatives: classifications.slice(1, 3), // 返回前3个候选意图
      };
      
    } catch (error) {
      this.logger.error('提取意图失败:', error);
      return {
        name: 'unknown',
        confidence: 0,
        alternatives: [],
      };
    }
  }

  /**
   * 提取实体
   * @param message 用户消息
   * @returns 实体列表
   */
  async extractEntities(message: string): Promise<any[]> {
    try {
      const entities: any[] = [];
      const lowerMessage = message.toLowerCase();
      
      // 设备实体识别
      const equipmentPatterns = [
        { pattern: /cnc|数控|机床/, type: 'equipment', value: 'CNC机床' },
        { pattern: /机器人|robot/, type: 'equipment', value: '机器人' },
        { pattern: /传送带|conveyor/, type: 'equipment', value: '传送带' },
        { pattern: /压缩机|compressor/, type: 'equipment', value: '压缩机' },
      ];
      
      // 操作实体识别
      const procedurePatterns = [
        { pattern: /维护|保养|maintenance/, type: 'procedure', value: '维护' },
        { pattern: /检查|inspection/, type: 'procedure', value: '检查' },
        { pattern: /清洁|清理|cleaning/, type: 'procedure', value: '清洁' },
        { pattern: /校准|calibration/, type: 'procedure', value: '校准' },
      ];
      
      // 症状实体识别
      const symptomPatterns = [
        { pattern: /不工作|故障|broken/, type: 'symptom', value: '设备故障' },
        { pattern: /噪音|noise/, type: 'symptom', value: '异常噪音' },
        { pattern: /过热|overheating/, type: 'symptom', value: '过热' },
        { pattern: /泄漏|leak/, type: 'symptom', value: '泄漏' },
      ];
      
      // 技能实体识别
      const skillPatterns = [
        { pattern: /操作|operation/, type: 'skill', value: '设备操作' },
        { pattern: /编程|programming/, type: 'skill', value: '编程' },
        { pattern: /调试|debugging/, type: 'skill', value: '调试' },
        { pattern: /安装|installation/, type: 'skill', value: '安装' },
      ];
      
      // 级别实体识别
      const levelPatterns = [
        { pattern: /初级|beginner|基础/, type: 'level', value: 'beginner' },
        { pattern: /中级|intermediate/, type: 'level', value: 'intermediate' },
        { pattern: /高级|advanced/, type: 'level', value: 'advanced' },
      ];
      
      // 应用所有模式
      const allPatterns = [
        ...equipmentPatterns,
        ...procedurePatterns,
        ...symptomPatterns,
        ...skillPatterns,
        ...levelPatterns,
      ];
      
      allPatterns.forEach(({ pattern, type, value }) => {
        if (pattern.test(lowerMessage)) {
          entities.push({
            type,
            value,
            confidence: 0.8,
            start: lowerMessage.search(pattern),
            end: lowerMessage.search(pattern) + value.length,
          });
        }
      });
      
      return entities;
      
    } catch (error) {
      this.logger.error('提取实体失败:', error);
      return [];
    }
  }

  /**
   * 文本预处理
   */
  private preprocessText(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s\u4e00-\u9fff]/g, '') // 保留中文字符
      .trim();
  }

  /**
   * 情感分析
   */
  private analyzeSentiment(text: string): any {
    try {
      const analyzer = new natural.SentimentAnalyzer('Chinese', 
        natural.PorterStemmer, 'afinn');
      const tokens = this.tokenizer.tokenize(text);
      const score = analyzer.getSentiment(tokens);
      
      let sentiment = 'neutral';
      if (score > 0.1) sentiment = 'positive';
      else if (score < -0.1) sentiment = 'negative';
      
      return {
        score,
        sentiment,
        confidence: Math.abs(score),
      };
    } catch (error) {
      return {
        score: 0,
        sentiment: 'neutral',
        confidence: 0,
      };
    }
  }

  /**
   * 关键词提取
   */
  private extractKeywords(tokens: string[]): string[] {
    // 简单的关键词提取，基于词频
    const stopWords = ['的', '是', '在', '有', '和', '了', '我', '你', '他', '她', '它'];
    const filteredTokens = tokens.filter(token => 
      token.length > 1 && !stopWords.includes(token)
    );
    
    // 计算词频
    const frequency: { [key: string]: number } = {};
    filteredTokens.forEach(token => {
      frequency[token] = (frequency[token] || 0) + 1;
    });
    
    // 返回频率最高的关键词
    return Object.keys(frequency)
      .sort((a, b) => frequency[b] - frequency[a])
      .slice(0, 5);
  }

  /**
   * 语言检测
   */
  private detectLanguage(text: string): string {
    // 简单的中英文检测
    const chinesePattern = /[\u4e00-\u9fff]/;
    const englishPattern = /[a-zA-Z]/;
    
    const hasChinese = chinesePattern.test(text);
    const hasEnglish = englishPattern.test(text);
    
    if (hasChinese && hasEnglish) return 'mixed';
    if (hasChinese) return 'zh-CN';
    if (hasEnglish) return 'en-US';
    return 'unknown';
  }
}
