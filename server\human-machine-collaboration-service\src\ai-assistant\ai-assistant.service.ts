import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { AIConversation } from '../database/entities/ai-conversation.entity';
import { NLPService } from './nlp.service';
import { KnowledgeBaseService } from './knowledge-base.service';

/**
 * AI智能助手服务
 * 提供智能对话、问答、建议等功能
 */
@Injectable()
export class AIAssistantService {
  private readonly logger = new Logger(AIAssistantService.name);

  constructor(
    @InjectRepository(AIConversation)
    private readonly conversationRepository: Repository<AIConversation>,
    private readonly nlpService: NLPService,
    private readonly knowledgeBaseService: KnowledgeBaseService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 处理用户消息
   * @param userId 用户ID
   * @param message 用户消息
   * @param context 上下文信息
   * @returns AI响应
   */
  async processMessage(
    userId: string,
    message: string,
    context?: any,
  ): Promise<any> {
    try {
      const startTime = Date.now();

      // 1. 自然语言理解
      const nlpResult = await this.nlpService.analyzeMessage(message);

      // 2. 意图识别和实体提取
      const intent = await this.nlpService.extractIntent(message);
      const entities = await this.nlpService.extractEntities(message);

      // 3. 根据意图生成响应
      let response: string;
      let conversationType = 'general';

      switch (intent.name) {
        case 'maintenance_question':
          response = await this.handleMaintenanceQuestion(message, entities, context);
          conversationType = 'maintenance';
          break;
        case 'troubleshooting_request':
          response = await this.handleTroubleshootingRequest(message, entities, context);
          conversationType = 'troubleshooting';
          break;
        case 'training_inquiry':
          response = await this.handleTrainingInquiry(message, entities, context);
          conversationType = 'training';
          break;
        case 'general_support':
          response = await this.handleGeneralSupport(message, entities, context);
          conversationType = 'support';
          break;
        default:
          response = await this.handleGeneralConversation(message, context);
          break;
      }

      const responseTime = Date.now() - startTime;

      // 4. 保存对话记录
      const conversation = await this.saveConversation({
        userId,
        userMessage: message,
        aiResponse: response,
        context,
        intent: {
          name: intent.name,
          confidence: intent.confidence,
          entities,
        },
        confidence: intent.confidence,
        responseTime,
        conversationType,
      });

      return {
        id: conversation.id,
        response,
        intent: intent.name,
        confidence: intent.confidence,
        entities,
        responseTime,
        suggestions: await this.generateSuggestions(intent, entities, context),
      };

    } catch (error) {
      this.logger.error('处理用户消息失败:', error);
      throw error;
    }
  }

  /**
   * 处理维护相关问题
   */
  private async handleMaintenanceQuestion(
    message: string,
    entities: any[],
    context?: any,
  ): Promise<string> {
    try {
      // 从知识库搜索相关维护信息
      const equipment = entities.find(e => e.type === 'equipment')?.value;
      const procedure = entities.find(e => e.type === 'procedure')?.value;

      const knowledgeResults = await this.knowledgeBaseService.searchMaintenance({
        equipment,
        procedure,
        query: message,
      });

      if (knowledgeResults.length > 0) {
        const bestMatch = knowledgeResults[0];
        return this.formatMaintenanceResponse(bestMatch);
      }

      return '抱歉，我没有找到相关的维护信息。请提供更具体的设备型号或维护步骤。';

    } catch (error) {
      this.logger.error('处理维护问题失败:', error);
      return '处理您的维护问题时出现错误，请稍后重试。';
    }
  }

  /**
   * 处理故障排除请求
   */
  private async handleTroubleshootingRequest(
    message: string,
    entities: any[],
    context?: any,
  ): Promise<string> {
    try {
      const symptom = entities.find(e => e.type === 'symptom')?.value;
      const equipment = entities.find(e => e.type === 'equipment')?.value;

      const troubleshootingSteps = await this.knowledgeBaseService.searchTroubleshooting({
        symptom,
        equipment,
        query: message,
      });

      if (troubleshootingSteps.length > 0) {
        return this.formatTroubleshootingResponse(troubleshootingSteps);
      }

      return '请描述具体的故障现象，我会为您提供相应的排除步骤。';

    } catch (error) {
      this.logger.error('处理故障排除请求失败:', error);
      return '处理您的故障排除请求时出现错误，请稍后重试。';
    }
  }

  /**
   * 处理培训咨询
   */
  private async handleTrainingInquiry(
    message: string,
    entities: any[],
    context?: any,
  ): Promise<string> {
    try {
      const skill = entities.find(e => e.type === 'skill')?.value;
      const level = entities.find(e => e.type === 'level')?.value;

      const trainingResources = await this.knowledgeBaseService.searchTraining({
        skill,
        level,
        query: message,
      });

      if (trainingResources.length > 0) {
        return this.formatTrainingResponse(trainingResources);
      }

      return '我可以为您推荐相关的培训资源。请告诉我您想学习的具体技能或操作。';

    } catch (error) {
      this.logger.error('处理培训咨询失败:', error);
      return '处理您的培训咨询时出现错误，请稍后重试。';
    }
  }

  /**
   * 处理一般支持
   */
  private async handleGeneralSupport(
    message: string,
    entities: any[],
    context?: any,
  ): Promise<string> {
    try {
      // 搜索常见问题解答
      const faqResults = await this.knowledgeBaseService.searchFAQ(message);

      if (faqResults.length > 0) {
        return faqResults[0].answer;
      }

      return '我是您的智能助手，可以帮助您解决维护、故障排除、培训等问题。请告诉我您需要什么帮助。';

    } catch (error) {
      this.logger.error('处理一般支持失败:', error);
      return '处理您的请求时出现错误，请稍后重试。';
    }
  }

  /**
   * 处理一般对话
   */
  private async handleGeneralConversation(
    message: string,
    context?: any,
  ): Promise<string> {
    // 简单的规则基础响应
    const lowerMessage = message.toLowerCase();

    if (lowerMessage.includes('你好') || lowerMessage.includes('hello')) {
      return '您好！我是您的智能助手，可以帮助您解决AR/VR维护指导、设备操作、故障排除等问题。有什么可以为您服务的吗？';
    }

    if (lowerMessage.includes('谢谢') || lowerMessage.includes('thank')) {
      return '不客气！如果您还有其他问题，随时可以问我。';
    }

    if (lowerMessage.includes('帮助') || lowerMessage.includes('help')) {
      return '我可以为您提供以下帮助：\n1. 设备维护指导\n2. 故障排除建议\n3. 操作培训资源\n4. AR/VR场景使用说明\n请告诉我您需要哪方面的帮助。';
    }

    return '我理解您的问题。请提供更多详细信息，这样我可以为您提供更准确的帮助。';
  }

  /**
   * 生成建议
   */
  private async generateSuggestions(
    intent: any,
    entities: any[],
    context?: any,
  ): Promise<string[]> {
    const suggestions: string[] = [];

    switch (intent.name) {
      case 'maintenance_question':
        suggestions.push(
          '查看维护手册',
          '观看操作视频',
          '联系技术支持',
        );
        break;
      case 'troubleshooting_request':
        suggestions.push(
          '运行诊断程序',
          '检查设备状态',
          '查看错误日志',
        );
        break;
      case 'training_inquiry':
        suggestions.push(
          '开始AR/VR培训',
          '查看培训计划',
          '预约实操训练',
        );
        break;
      default:
        suggestions.push(
          '我需要维护帮助',
          '设备出现故障',
          '我想学习新技能',
        );
        break;
    }

    return suggestions;
  }

  /**
   * 保存对话记录
   */
  private async saveConversation(data: any): Promise<AIConversation> {
    const conversation = this.conversationRepository.create(data);
    return await this.conversationRepository.save(conversation);
  }

  /**
   * 格式化维护响应
   */
  private formatMaintenanceResponse(knowledgeItem: any): string {
    return `根据您的问题，我找到了相关的维护信息：\n\n${knowledgeItem.content}\n\n如果您需要更详细的指导，我可以为您启动AR/VR维护场景。`;
  }

  /**
   * 格式化故障排除响应
   */
  private formatTroubleshootingResponse(steps: any[]): string {
    let response = '请按照以下步骤进行故障排除：\n\n';
    steps.forEach((step, index) => {
      response += `${index + 1}. ${step.description}\n`;
    });
    response += '\n如果问题仍未解决，请联系技术支持。';
    return response;
  }

  /**
   * 格式化培训响应
   */
  private formatTrainingResponse(resources: any[]): string {
    let response = '为您推荐以下培训资源：\n\n';
    resources.forEach((resource, index) => {
      response += `${index + 1}. ${resource.title} - ${resource.description}\n`;
    });
    response += '\n您可以选择开始AR/VR培训场景进行实践学习。';
    return response;
  }

  /**
   * 获取对话历史
   * @param userId 用户ID
   * @param limit 限制数量
   * @returns 对话历史
   */
  async getConversationHistory(userId: string, limit: number = 10): Promise<AIConversation[]> {
    return await this.conversationRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
      take: limit,
    });
  }

  /**
   * 更新对话反馈
   * @param conversationId 对话ID
   * @param feedback 反馈信息
   */
  async updateFeedback(conversationId: string, feedback: any): Promise<void> {
    await this.conversationRepository.update(conversationId, { feedback });
  }

  /**
   * 获取AI助手统计信息
   */
  async getStatistics(): Promise<any> {
    const totalConversations = await this.conversationRepository.count();
    const avgResponseTime = await this.conversationRepository
      .createQueryBuilder('conversation')
      .select('AVG(conversation.responseTime)', 'avgResponseTime')
      .getRawOne();

    return {
      totalConversations,
      averageResponseTime: avgResponseTime?.avgResponseTime || 0,
    };
  }
}
